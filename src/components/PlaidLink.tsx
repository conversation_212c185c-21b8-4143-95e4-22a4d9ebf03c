
import { useState } from 'react';
import { usePlaidLink } from 'react-plaid-link';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { CreditCard, Loader2 } from 'lucide-react';
import { useToast } from '@/hooks/use-toast';
import { supabase } from '@/integrations/supabase/client';
import { useAuth } from '@/contexts/AuthContext';

interface PlaidLinkProps {
  onSuccess?: () => void;
}

export const PlaidLink = ({ onSuccess }: PlaidLinkProps) => {
  const [linkToken, setLinkToken] = useState<string | null>(null);
  const [loading, setLoading] = useState(false);
  const { toast } = useToast();
  const { session, user } = useAuth();

  // Debug logging
  console.log('PlaidLink component rendered');
  console.log('Session:', session);
  console.log('User:', user);
  console.log('Session exists:', !!session);
  console.log('User exists:', !!user);

  const { open, ready } = usePlaidLink({
    token: linkToken,
    onSuccess: async (public_token, metadata) => {
      console.log('Plaid Link success:', { public_token, metadata });
      setLoading(true);

      try {
        if (!session) {
          throw new Error('No active session');
        }

        const { data, error } = await supabase.functions.invoke('plaid-operations', {
          body: {
            action: 'exchange_public_token',
            public_token,
          },
          headers: {
            Authorization: `Bearer ${session.access_token}`,
          },
        });

        if (error) {
          throw error;
        }

        if (data.error) {
          throw new Error(data.error);
        }

        toast({
          title: "Account linked successfully!",
          description: `${data.accounts} account(s) have been linked to your GiveRound account.`,
        });

        onSuccess?.();
      } catch (error) {
        console.error('Token exchange error:', error);
        toast({
          title: "Failed to link account",
          description: error instanceof Error ? error.message : "Please try again later.",
          variant: "destructive",
        });
      } finally {
        setLoading(false);
      }
    },
    onExit: (err, metadata) => {
      console.log('Plaid Link exit:', { err, metadata });
    },
  });

  const handleLinkAccount = async () => {
    console.log('handleLinkAccount called');
    console.log('Current session:', session);
    console.log('Loading state:', loading);
    console.log('Button disabled?', loading || !session);

    setLoading(true);

    try {
      if (!session) {
        console.error('No active session found');
        throw new Error('No active session');
      }

      console.log('Calling Supabase function with:', {
        action: 'create_link_token',
        authHeader: `Bearer ${session.access_token.substring(0, 20)}...`
      });

      const { data, error } = await supabase.functions.invoke('plaid-operations', {
        body: {
          action: 'create_link_token',
        },
        headers: {
          Authorization: `Bearer ${session.access_token}`,
        },
      });

      console.log('Supabase function response:', { data, error });

      if (error) {
        console.error('Supabase function error:', error);
        throw error;
      }

      if (data.error) {
        console.error('Plaid API error:', data.error);
        throw new Error(data.error);
      }

      setLinkToken(data.link_token);
      
      // Open Plaid Link once we have the token
      setTimeout(() => {
        if (ready) {
          open();
        }
      }, 100);
    } catch (error) {
      console.error('Link token creation error:', error);
      toast({
        title: "Failed to initialize account linking",
        description: error instanceof Error ? error.message : "Please try again later.",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <CreditCard className="h-5 w-5" />
          Link Your Card or Account
        </CardTitle>
        <CardDescription>
          Connect your debit card, credit card, or bank account to automatically round up purchases and donate the change.
          {!session && <div className="text-red-500 text-sm mt-2">⚠️ No active session detected</div>}
        </CardDescription>
      </CardHeader>
      <CardContent>
        <Button
          onClick={() => {
            console.log('Button clicked!');
            console.log('Button disabled?', loading || !session);
            console.log('Loading:', loading);
            console.log('Session exists:', !!session);
            if (!session) {
              alert('No session found! Please log in first.');
              return;
            }
            handleLinkAccount();
          }}
          disabled={loading || !session}
          className="w-full"
        >
          {loading ? (
            <>
              <Loader2 className="mr-2 h-4 w-4 animate-spin" />
              Connecting...
            </>
          ) : (
            <>
              <CreditCard className="mr-2 h-4 w-4" />
              Link Card or Account
            </>
          )}
        </Button>

        {/* Test button for debugging */}
        <Button
          onClick={() => {
            console.log('Test button clicked!');
            alert('Test button works!');
          }}
          variant="outline"
          className="w-full mt-2"
        >
          Test Button (Debug)
        </Button>
      </CardContent>
    </Card>
  );
};
