
import { useState } from "react";
import { <PERSON> } from "react-router-dom";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { ArrowDown, Check, Users, Bell } from "lucide-react";

const Index = () => {
  const [isScrolled, setIsScrolled] = useState(false);

  return (
    <div className="min-h-screen bg-background">
      {/* Navigation */}
      <nav className="fixed top-0 w-full z-50 bg-background/95 backdrop-blur-sm border-b">
        <div className="container mx-auto px-4 py-4 flex justify-between items-center">
          <div className="text-2xl font-bold text-primary">GiveRound</div>
          <div className="flex gap-4">
            <Link to="/login">
              <Button variant="ghost">Log In</Button>
            </Link>
            <Link to="/signup">
              <Button>Get Started</Button>
            </Link>
          </div>
        </div>
      </nav>

      {/* Hero Section */}
      <section className="pt-24 pb-16 px-4">
        <div className="container mx-auto text-center max-w-4xl">
          <h1 className="text-5xl md:text-6xl font-bold mb-6 bg-gradient-to-r from-primary to-green-600 bg-clip-text text-transparent">
            Turn Spare Change Into Real Change
          </h1>
          <p className="text-xl text-muted-foreground mb-8 max-w-2xl mx-auto">
            Round up your everyday purchases and automatically donate the spare change to nonprofits, churches, and causes you care about.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Link to="/signup">
              <Button size="lg" className="bg-gradient-to-r from-primary to-green-600 hover:from-primary/90 hover:to-green-600/90">
                Start Giving Today
              </Button>
            </Link>
            <Button variant="outline" size="lg">
              Watch Demo
            </Button>
          </div>
          
          {/* Hero Visual */}
          <div className="mt-16 relative">
            <div className="bg-gradient-to-r from-blue-50 to-green-50 rounded-2xl p-8 shadow-2xl">
              <div className="grid grid-cols-1 md:grid-cols-3 gap-6 text-left">
                <div className="space-y-2">
                  <div className="text-sm text-muted-foreground">Coffee Purchase</div>
                  <div className="text-2xl font-bold">$4.25</div>
                  <div className="text-green-600 font-medium">+$0.75 rounded up</div>
                </div>
                <ArrowDown className="mx-auto text-muted-foreground" size={24} />
                <div className="space-y-2">
                  <div className="text-sm text-muted-foreground">Donated This Week</div>
                  <div className="text-2xl font-bold text-green-600">$12.50</div>
                  <div className="text-sm text-muted-foreground">To Local Food Bank</div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section className="py-16 px-4 bg-gradient-to-b from-background to-muted/30">
        <div className="container mx-auto max-w-6xl">
          <h2 className="text-3xl font-bold text-center mb-12">How GiveRound Works</h2>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            <Card className="border-0 shadow-lg bg-card/50 backdrop-blur-sm">
              <CardHeader>
                <div className="w-12 h-12 bg-primary/10 rounded-lg flex items-center justify-center mb-4">
                  <Check className="text-primary" size={24} />
                </div>
                <CardTitle>Link Your Cards</CardTitle>
                <CardDescription>
                  Securely connect your debit and credit cards. We use bank-level encryption to keep your data safe.
                </CardDescription>
              </CardHeader>
            </Card>

            <Card className="border-0 shadow-lg bg-card/50 backdrop-blur-sm">
              <CardHeader>
                <div className="w-12 h-12 bg-green-600/10 rounded-lg flex items-center justify-center mb-4">
                  <ArrowDown className="text-green-600" size={24} />
                </div>
                <CardTitle>Round Up Purchases</CardTitle>
                <CardDescription>
                  Every purchase automatically rounds up to the nearest dollar. Your spare change adds up fast!
                </CardDescription>
              </CardHeader>
            </Card>

            <Card className="border-0 shadow-lg bg-card/50 backdrop-blur-sm">
              <CardHeader>
                <div className="w-12 h-12 bg-blue-600/10 rounded-lg flex items-center justify-center mb-4">
                  <Users className="text-blue-600" size={24} />
                </div>
                <CardTitle>Support Causes</CardTitle>
                <CardDescription>
                  Choose from thousands of verified nonprofits and churches. See your impact with real-time updates.
                </CardDescription>
              </CardHeader>
            </Card>
          </div>
        </div>
      </section>

      {/* Stats Section */}
      <section className="py-16 px-4">
        <div className="container mx-auto max-w-4xl text-center">
          <div className="grid grid-cols-2 md:grid-cols-4 gap-8">
            <div>
              <div className="text-3xl font-bold text-primary">$2.3M+</div>
              <div className="text-muted-foreground">Donated</div>
            </div>
            <div>
              <div className="text-3xl font-bold text-green-600">50K+</div>
              <div className="text-muted-foreground">Active Users</div>
            </div>
            <div>
              <div className="text-3xl font-bold text-blue-600">1,200+</div>
              <div className="text-muted-foreground">Partner Nonprofits</div>
            </div>
            <div>
              <div className="text-3xl font-bold text-purple-600">98%</div>
              <div className="text-muted-foreground">User Satisfaction</div>
            </div>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-16 px-4 bg-gradient-to-r from-primary to-green-600">
        <div className="container mx-auto max-w-4xl text-center text-white">
          <h2 className="text-4xl font-bold mb-6">Ready to Make a Difference?</h2>
          <p className="text-xl mb-8 opacity-90">
            Join thousands of users who are changing the world one round-up at a time.
          </p>
          <Link to="/signup">
            <Button size="lg" variant="secondary" className="bg-white text-primary hover:bg-white/90">
              Get Started Free
            </Button>
          </Link>
        </div>
      </section>

      {/* Footer */}
      <footer className="py-12 px-4 bg-muted/30">
        <div className="container mx-auto max-w-6xl">
          <div className="grid grid-cols-2 md:grid-cols-4 gap-8">
            <div>
              <div className="font-bold text-primary mb-4">GiveRound</div>
              <div className="text-sm text-muted-foreground">
                Making charitable giving effortless through micro-donations.
              </div>
            </div>
            <div>
              <div className="font-semibold mb-4">Product</div>
              <div className="space-y-2 text-sm text-muted-foreground">
                <div>How it Works</div>
                <div>Security</div>
                <div>Pricing</div>
              </div>
            </div>
            <div>
              <div className="font-semibold mb-4">Support</div>
              <div className="space-y-2 text-sm text-muted-foreground">
                <div>Help Center</div>
                <div>Contact Us</div>
                <div>Privacy</div>
              </div>
            </div>
            <div>
              <div className="font-semibold mb-4">Connect</div>
              <div className="space-y-2 text-sm text-muted-foreground">
                <div>Blog</div>
                <div>Newsletter</div>
                <div>Social Media</div>
              </div>
            </div>
          </div>
        </div>
      </footer>
    </div>
  );
};

export default Index;
