
import { useState, useEffect } from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Progress } from "@/components/ui/progress";
import { useAuth } from "@/contexts/AuthContext";
import {
  getUserDashboardSummary,
  getUserTransactions,
  getUserCharityFollows,
  getUserDonations
} from "@/lib/database";
import { createSampleTransactions } from "@/lib/sampleData";
import { toast } from "sonner";

const Dashboard = () => {
  const { user, userProfile } = useAuth();
  const [dashboardData, setDashboardData] = useState<any>(null);
  const [recentTransactions, setRecentTransactions] = useState<any[]>([]);
  const [favoriteCharities, setFavoriteCharities] = useState<any[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    console.log('Dashboard useEffect triggered. User:', user, 'UserProfile:', userProfile);

    const fetchDashboardData = async () => {
      try {
        setLoading(true);
        setError(null);

        // Add timeout to prevent infinite loading
        const timeoutId = setTimeout(() => {
          console.log('Dashboard loading timeout - forcing completion');
          setDashboardData({
            user_id: 'default',
            full_name: 'User',
            monthly_goal: 50,
            total_donated_this_month: 0,
            total_round_ups_this_month: 0,
            charities_supported: 0
          });
          setRecentTransactions([]);
          setFavoriteCharities([]);
          setLoading(false);
        }, 3000);

        // Clear any corrupted localStorage data
        try {
          localStorage.removeItem('sb-ltmmbkckohkusutbsbsu-auth-token');
        } catch (e) {
          console.warn('Could not clear localStorage:', e);
        }

        console.log('Setting default dashboard data...');
        setDashboardData({
          user_id: user?.id || 'default',
          full_name: userProfile?.full_name || 'User',
          monthly_goal: 50,
          total_donated_this_month: 0,
          total_round_ups_this_month: 0,
          charities_supported: 0
        });

        setRecentTransactions([]);
        setFavoriteCharities([]);

        console.log('Dashboard data set successfully');
        clearTimeout(timeoutId);

      } catch (error) {
        console.error('Error in dashboard:', error);
        setError('Failed to load dashboard');
      } finally {
        setLoading(false);
      }
    };

    fetchDashboardData();
  }, [user, userProfile]);

  if (loading) {
    return (
      <div className="min-h-screen bg-background p-4 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
          <p>Loading your dashboard...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="min-h-screen bg-background p-4 flex items-center justify-center">
        <div className="text-center">
          <div className="text-red-500 mb-4">⚠️ Error</div>
          <p className="text-muted-foreground mb-4">{error}</p>
          <Button onClick={() => window.location.reload()}>
            Retry
          </Button>
        </div>
      </div>
    );
  }

  const monthlyGoal = 50;
  const currentDonations = 0;
  const totalDonated = 0;
  const progress = 0;

  const handleCreateSampleData = async () => {
    if (!user) return;

    try {
      await createSampleTransactions(user.id);
      toast.success('Sample transactions created! Refresh to see them.');
      // Refresh the data
      window.location.reload();
    } catch (error) {
      toast.error('Failed to create sample transactions');
    }
  };

  return (
    <div className="min-h-screen bg-background p-4">
      <div className="container mx-auto max-w-6xl">
        {/* Header */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold mb-2">Your Impact Dashboard</h1>
          <p className="text-muted-foreground">Welcome! Your dashboard is loading...</p>
        </div>

        {/* Simple Stats */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
          <Card>
            <CardHeader>
              <CardTitle>This Month</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">$0.00</div>
              <p className="text-sm text-muted-foreground">No donations yet</p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Total Donated</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">$0.00</div>
              <p className="text-sm text-muted-foreground">Since joining</p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Transactions</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">0</div>
              <p className="text-sm text-muted-foreground">Recent transactions</p>
            </CardContent>
          </Card>
        </div>

        {/* Welcome Message */}
        <Card>
          <CardHeader>
            <CardTitle>Welcome to Your Giving Dashboard!</CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-muted-foreground">
              Your dashboard is ready! Start by connecting your bank account to track round-ups
              and browse charities to support causes you care about.
            </p>
            <div className="mt-4 space-x-4">
              <Button>Connect Bank Account</Button>
              <Button variant="outline">Browse Charities</Button>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
};

export default Dashboard;
