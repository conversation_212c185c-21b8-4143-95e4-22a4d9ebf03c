
import { useState } from "react";
import { <PERSON>, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Progress } from "@/components/ui/progress";

const Dashboard = () => {
  const [monthlyGoal] = useState(50);
  const [currentDonations] = useState(32.75);

  const recentTransactions = [
    { id: 1, vendor: "Starbucks", amount: 4.25, roundUp: 0.75, date: "Today" },
    { id: 2, vendor: "Target", amount: 23.47, roundUp: 0.53, date: "Yesterday" },
    { id: 3, vendor: "Gas Station", amount: 45.12, roundUp: 0.88, date: "2 days ago" },
    { id: 4, vendor: "Grocery Store", amount: 67.89, roundUp: 0.11, date: "3 days ago" },
  ];

  const favoriteCharities = [
    { id: 1, name: "Local Food Bank", allocation: 40, donated: 13.10 },
    { id: 2, name: "Children's Hospital", allocation: 35, donated: 11.46 },
    { id: 3, name: "Animal Shelter", allocation: 25, donated: 8.19 },
  ];

  const progress = (currentDonations / monthlyGoal) * 100;

  return (
    <div className="min-h-screen bg-background p-4">
      <div className="container mx-auto max-w-6xl">
        {/* Header */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold mb-2">Your Impact Dashboard</h1>
          <p className="text-muted-foreground">Track your giving and see the difference you're making</p>
        </div>

        {/* Stats Overview */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
          <Card className="bg-gradient-to-r from-green-600 to-green-700 text-white border-0">
            <CardHeader className="pb-2">
              <CardTitle className="text-lg">This Month</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-3xl font-bold">${currentDonations}</div>
              <div className="text-green-100 text-sm">+$4.20 this week</div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-lg">Total Donated</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-3xl font-bold text-primary">$347.65</div>
              <div className="text-muted-foreground text-sm">Since joining</div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-lg">Round-Ups</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-3xl font-bold text-blue-600">127</div>
              <div className="text-muted-foreground text-sm">Transactions this month</div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-lg">Impact Score</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-3xl font-bold text-purple-600">8.5</div>
              <div className="text-muted-foreground text-sm">Out of 10</div>
            </CardContent>
          </Card>
        </div>

        {/* Monthly Goal Progress */}
        <Card className="mb-8">
          <CardHeader>
            <CardTitle>Monthly Goal Progress</CardTitle>
            <CardDescription>
              You're {Math.round(progress)}% of the way to your ${monthlyGoal} monthly goal
            </CardDescription>
          </CardHeader>
          <CardContent>
            <Progress value={progress} className="mb-4" />
            <div className="flex justify-between text-sm text-muted-foreground">
              <span>${currentDonations} raised</span>
              <span>${monthlyGoal - currentDonations} remaining</span>
            </div>
          </CardContent>
        </Card>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          {/* Recent Transactions */}
          <Card>
            <CardHeader>
              <CardTitle>Recent Round-Ups</CardTitle>
              <CardDescription>Your latest transactions and donations</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {recentTransactions.map((transaction) => (
                  <div key={transaction.id} className="flex justify-between items-center p-3 rounded-lg bg-muted/30">
                    <div>
                      <div className="font-medium">{transaction.vendor}</div>
                      <div className="text-sm text-muted-foreground">{transaction.date}</div>
                    </div>
                    <div className="text-right">
                      <div className="font-medium">${transaction.amount}</div>
                      <div className="text-sm text-green-600">+${transaction.roundUp}</div>
                    </div>
                  </div>
                ))}
              </div>
              <Button variant="outline" className="w-full mt-4">
                View All Transactions
              </Button>
            </CardContent>
          </Card>

          {/* Charity Allocations */}
          <Card>
            <CardHeader>
              <CardTitle>Your Favorite Causes</CardTitle>
              <CardDescription>How your donations are distributed</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-6">
                {favoriteCharities.map((charity) => (
                  <div key={charity.id} className="space-y-2">
                    <div className="flex justify-between items-center">
                      <div className="font-medium">{charity.name}</div>
                      <div className="text-sm text-muted-foreground">{charity.allocation}%</div>
                    </div>
                    <Progress value={charity.allocation} className="h-2" />
                    <div className="text-sm text-muted-foreground">
                      ${charity.donated} donated this month
                    </div>
                  </div>
                ))}
              </div>
              <Button variant="outline" className="w-full mt-4">
                Manage Allocations
              </Button>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
};

export default Dashboard;
