
import { useState, useEffect } from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Progress } from "@/components/ui/progress";
import { useAuth } from "@/contexts/AuthContext";
import {
  getUserDashboardSummary,
  getUserTransactions,
  getUserCharityFollows,
  getUserDonations
} from "@/lib/database";
import { createSampleTransactions } from "@/lib/sampleData";
import { toast } from "sonner";

const Dashboard = () => {
  const { user, userProfile } = useAuth();
  const [dashboardData, setDashboardData] = useState<any>(null);
  const [recentTransactions, setRecentTransactions] = useState<any[]>([]);
  const [favoriteCharities, setFavoriteCharities] = useState<any[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const fetchDashboardData = async () => {
      if (!user) return;

      try {
        // Fetch dashboard summary
        const { data: summary, error: summaryError } = await getUserDashboardSummary(user.id);
        if (summaryError) {
          console.warn('Dashboard summary not available:', summaryError);
          // Create a default summary if the view doesn't exist
          setDashboardData({
            user_id: user.id,
            full_name: userProfile?.full_name || '',
            monthly_goal: userProfile?.monthly_goal || 50,
            total_donated_this_month: 0,
            total_round_ups_this_month: 0,
            charities_supported: 0
          });
        } else {
          setDashboardData(summary);
        }

        // Fetch recent transactions
        const { data: transactions, error: transactionsError } = await getUserTransactions(user.id, 4);
        if (transactionsError) {
          console.warn('Transactions not available:', transactionsError);
        }
        setRecentTransactions(transactions || []);

        // Fetch favorite charities
        const { data: charities, error: charitiesError } = await getUserCharityFollows(user.id);
        if (charitiesError) {
          console.warn('Charity follows not available:', charitiesError);
        }
        setFavoriteCharities(charities || []);

      } catch (error) {
        console.error('Error fetching dashboard data:', error);
        // Set default data to prevent crashes
        setDashboardData({
          user_id: user.id,
          full_name: userProfile?.full_name || '',
          monthly_goal: userProfile?.monthly_goal || 50,
          total_donated_this_month: 0,
          total_round_ups_this_month: 0,
          charities_supported: 0
        });
      } finally {
        setLoading(false);
      }
    };

    fetchDashboardData();
  }, [user]);

  if (loading) {
    return (
      <div className="min-h-screen bg-background p-4 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
          <p>Loading your dashboard...</p>
        </div>
      </div>
    );
  }

  const monthlyGoal = userProfile?.monthly_goal || dashboardData?.monthly_goal || 50;
  const currentDonations = dashboardData?.total_donated_this_month || 0;
  const totalDonated = userProfile?.total_donated || 0;
  const progress = monthlyGoal > 0 ? (currentDonations / monthlyGoal) * 100 : 0;

  const handleCreateSampleData = async () => {
    if (!user) return;

    try {
      await createSampleTransactions(user.id);
      toast.success('Sample transactions created! Refresh to see them.');
      // Refresh the data
      window.location.reload();
    } catch (error) {
      toast.error('Failed to create sample transactions');
    }
  };

  return (
    <div className="min-h-screen bg-background p-4">
      <div className="container mx-auto max-w-6xl">
        {/* Header */}
        <div className="mb-8">
          <div className="flex justify-between items-start">
            <div>
              <h1 className="text-3xl font-bold mb-2">Your Impact Dashboard</h1>
              <p className="text-muted-foreground">Track your giving and see the difference you're making</p>
            </div>
            {/* Development helper button */}
            {recentTransactions.length === 0 && (
              <Button
                variant="outline"
                size="sm"
                onClick={handleCreateSampleData}
                className="text-xs"
              >
                Add Sample Data
              </Button>
            )}
          </div>
        </div>

        {/* Stats Overview */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
          <Card className="bg-gradient-to-r from-green-600 to-green-700 text-white border-0">
            <CardHeader className="pb-2">
              <CardTitle className="text-lg">This Month</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-3xl font-bold">${currentDonations.toFixed(2)}</div>
              <div className="text-green-100 text-sm">
                {dashboardData?.total_round_ups_this_month ?
                  `+$${dashboardData.total_round_ups_this_month.toFixed(2)} in round-ups` :
                  'No round-ups yet'
                }
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-lg">Total Donated</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-3xl font-bold text-primary">${totalDonated.toFixed(2)}</div>
              <div className="text-muted-foreground text-sm">Since joining</div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-lg">Transactions</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-3xl font-bold text-blue-600">{recentTransactions.length}</div>
              <div className="text-muted-foreground text-sm">Recent transactions</div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-lg">Charities Supported</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-3xl font-bold text-purple-600">
                {dashboardData?.charities_supported || favoriteCharities.length}
              </div>
              <div className="text-muted-foreground text-sm">Organizations helped</div>
            </CardContent>
          </Card>
        </div>

        {/* Monthly Goal Progress */}
        <Card className="mb-8">
          <CardHeader>
            <CardTitle>Monthly Goal Progress</CardTitle>
            <CardDescription>
              You're {Math.round(progress)}% of the way to your ${monthlyGoal} monthly goal
            </CardDescription>
          </CardHeader>
          <CardContent>
            <Progress value={progress} className="mb-4" />
            <div className="flex justify-between text-sm text-muted-foreground">
              <span>${currentDonations} raised</span>
              <span>${monthlyGoal - currentDonations} remaining</span>
            </div>
          </CardContent>
        </Card>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          {/* Recent Transactions */}
          <Card>
            <CardHeader>
              <CardTitle>Recent Round-Ups</CardTitle>
              <CardDescription>Your latest transactions and donations</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {recentTransactions.length > 0 ? (
                  recentTransactions.map((transaction) => (
                    <div key={transaction.id} className="flex justify-between items-center p-3 rounded-lg bg-muted/30">
                      <div>
                        <div className="font-medium">{transaction.vendor_name}</div>
                        <div className="text-sm text-muted-foreground">
                          {new Date(transaction.transaction_date).toLocaleDateString()}
                        </div>
                      </div>
                      <div className="text-right">
                        <div className="font-medium">${transaction.purchase_amount.toFixed(2)}</div>
                        <div className="text-sm text-green-600">+${transaction.round_up_amount.toFixed(2)}</div>
                      </div>
                    </div>
                  ))
                ) : (
                  <div className="text-center py-8 text-muted-foreground">
                    <p>No transactions yet</p>
                    <p className="text-sm">Connect your bank account to start tracking round-ups</p>
                  </div>
                )}
              </div>
              <Button variant="outline" className="w-full mt-4">
                View All Transactions
              </Button>
            </CardContent>
          </Card>

          {/* Charity Allocations */}
          <Card>
            <CardHeader>
              <CardTitle>Your Favorite Causes</CardTitle>
              <CardDescription>How your donations are distributed</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-6">
                {favoriteCharities.length > 0 ? (
                  favoriteCharities.map((follow) => (
                    <div key={follow.id} className="space-y-2">
                      <div className="flex justify-between items-center">
                        <div className="font-medium">{follow.charities?.name}</div>
                        <div className="text-sm text-muted-foreground">{follow.allocation_percentage}%</div>
                      </div>
                      <Progress value={follow.allocation_percentage} className="h-2" />
                      <div className="text-sm text-muted-foreground">
                        ${(currentDonations * (follow.allocation_percentage / 100)).toFixed(2)} allocated this month
                      </div>
                    </div>
                  ))
                ) : (
                  <div className="text-center py-8 text-muted-foreground">
                    <p>No charities followed yet</p>
                    <p className="text-sm">Browse charities to start supporting causes you care about</p>
                  </div>
                )}
              </div>
              <Button variant="outline" className="w-full mt-4">
                Manage Allocations
              </Button>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
};

export default Dashboard;
