
import { useState } from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import { Check, Users } from "lucide-react";

const Charities = () => {
  const [searchTerm, setSearchTerm] = useState("");
  const [selectedCategory, setSelectedCategory] = useState("all");

  const categories = [
    { id: "all", name: "All Causes" },
    { id: "health", name: "Health" },
    { id: "education", name: "Education" },
    { id: "environment", name: "Environment" },
    { id: "poverty", name: "Poverty" },
    { id: "animals", name: "Animals" },
    { id: "religion", name: "Religious" },
  ];

  const charities = [
    {
      id: 1,
      name: "Local Food Bank",
      category: "poverty",
      description: "Fighting hunger in our community by providing nutritious meals to families in need.",
      impact: "Feeds 2,500 families weekly",
      verified: true,
      rating: 4.8,
      donors: 1250,
      totalRaised: "$2.3M",
      isFollowing: true,
    },
    {
      id: 2,
      name: "Children's Hospital Foundation",
      category: "health",
      description: "Supporting pediatric care and research to give children the best possible health outcomes.",
      impact: "Treated 15,000 children last year",
      verified: true,
      rating: 4.9,
      donors: 2100,
      totalRaised: "$5.1M",
      isFollowing: false,
    },
    {
      id: 3,
      name: "Ocean Conservation Society",
      category: "environment",
      description: "Protecting marine ecosystems and wildlife through research and conservation efforts.",
      impact: "Protected 50,000 acres of ocean",
      verified: true,
      rating: 4.7,
      donors: 890,
      totalRaised: "$1.8M",
      isFollowing: false,
    },
    {
      id: 4,
      name: "Animal Rescue Shelter",
      category: "animals",
      description: "Providing shelter, care, and loving homes for abandoned and rescued animals.",
      impact: "Rescued 3,200 animals this year",
      verified: true,
      rating: 4.8,
      donors: 1650,
      totalRaised: "$980K",
      isFollowing: true,
    },
    {
      id: 5,
      name: "Education First Foundation",
      category: "education",
      description: "Ensuring every child has access to quality education and learning resources.",
      impact: "Educated 8,500 students",
      verified: true,
      rating: 4.6,
      donors: 750,
      totalRaised: "$1.2M",
      isFollowing: false,
    },
    {
      id: 6,
      name: "St. Mary's Community Church",
      category: "religion",
      description: "Supporting our local community through faith, fellowship, and charitable outreach programs.",
      impact: "Serves 500 community members",
      verified: true,
      rating: 4.9,
      donors: 320,
      totalRaised: "$450K",
      isFollowing: false,
    },
  ];

  const filteredCharities = charities.filter(charity => {
    const matchesSearch = charity.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         charity.description.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesCategory = selectedCategory === "all" || charity.category === selectedCategory;
    return matchesSearch && matchesCategory;
  });

  return (
    <div className="min-h-screen bg-background p-4">
      <div className="container mx-auto max-w-6xl">
        {/* Header */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold mb-2">Discover Causes</h1>
          <p className="text-muted-foreground">Find verified nonprofits and churches to support with your donations</p>
        </div>

        {/* Search and Filters */}
        <div className="mb-8 space-y-4">
          <Input
            placeholder="Search for causes, organizations, or keywords..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="max-w-md"
          />
          
          <div className="flex flex-wrap gap-2">
            {categories.map((category) => (
              <Button
                key={category.id}
                variant={selectedCategory === category.id ? "default" : "outline"}
                size="sm"
                onClick={() => setSelectedCategory(category.id)}
              >
                {category.name}
              </Button>
            ))}
          </div>
        </div>

        {/* Charity Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {filteredCharities.map((charity) => (
            <Card key={charity.id} className="hover:shadow-lg transition-shadow">
              <CardHeader>
                <div className="flex items-start justify-between">
                  <div className="flex-1">
                    <CardTitle className="text-lg flex items-center gap-2">
                      {charity.name}
                      {charity.verified && (
                        <Check className="w-4 h-4 text-green-600" />
                      )}
                    </CardTitle>
                    <CardDescription className="mt-2">
                      {charity.description}
                    </CardDescription>
                  </div>
                </div>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex items-center gap-2 text-sm text-muted-foreground">
                  <Users className="w-4 h-4" />
                  <span>{charity.donors.toLocaleString()} donors</span>
                  <span>•</span>
                  <span>{charity.totalRaised} raised</span>
                </div>

                <div className="space-y-2">
                  <Badge variant="secondary" className="text-xs">
                    {categories.find(c => c.id === charity.category)?.name}
                  </Badge>
                  <div className="text-sm font-medium text-green-600">
                    {charity.impact}
                  </div>
                </div>

                <div className="flex gap-2">
                  <Button
                    variant={charity.isFollowing ? "default" : "outline"}
                    size="sm"
                    className="flex-1"
                  >
                    {charity.isFollowing ? "Following" : "Follow"}
                  </Button>
                  <Button variant="outline" size="sm">
                    Donate Now
                  </Button>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>

        {filteredCharities.length === 0 && (
          <div className="text-center py-12">
            <p className="text-muted-foreground">No charities found matching your criteria.</p>
            <Button variant="outline" className="mt-4" onClick={() => {
              setSearchTerm("");
              setSelectedCategory("all");
            }}>
              Clear Filters
            </Button>
          </div>
        )}
      </div>
    </div>
  );
};

export default Charities;
