
import { useState, useEffect } from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import { Check, Users, Heart, DollarSign } from "lucide-react";
import { useAuth } from "@/contexts/AuthContext";
import {
  getCharities,
  getCharityCategories,
  followCharity,
  unfollowCharity,
  getUserCharityFollows
} from "@/lib/database";
import { toast } from "sonner";

const Charities = () => {
  const { user } = useAuth();
  const [searchTerm, setSearchTerm] = useState("");
  const [selectedCategory, setSelectedCategory] = useState("all");
  const [charities, setCharities] = useState<any[]>([]);
  const [categories, setCategories] = useState<any[]>([]);
  const [userFollows, setUserFollows] = useState<any[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const fetchData = async () => {
      try {
        // Fetch categories
        const { data: categoriesData } = await getCharityCategories();
        const allCategories = [
          { id: 0, name: "all", description: "All Causes" },
          ...(categoriesData || [])
        ];
        setCategories(allCategories);

        // Fetch charities
        const { data: charitiesData } = await getCharities();
        setCharities(charitiesData || []);

        // Fetch user follows if logged in
        if (user) {
          const { data: followsData } = await getUserCharityFollows(user.id);
          setUserFollows(followsData || []);
        }
      } catch (error) {
        console.error('Error fetching data:', error);
        toast.error('Failed to load charities');
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, [user]);

  const handleFollowToggle = async (charityId: number, isCurrentlyFollowing: boolean) => {
    if (!user) {
      toast.error('Please log in to follow charities');
      return;
    }

    try {
      if (isCurrentlyFollowing) {
        await unfollowCharity(user.id, charityId);
        setUserFollows(prev => prev.filter(follow => follow.charity_id !== charityId));
        toast.success('Charity unfollowed');
      } else {
        await followCharity(user.id, charityId);
        setUserFollows(prev => [...prev, { charity_id: charityId }]);
        toast.success('Charity followed');
      }
    } catch (error) {
      console.error('Error toggling follow:', error);
      toast.error('Failed to update follow status');
    }
  };

  const filteredCharities = charities.filter(charity => {
    const matchesSearch = charity.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         (charity.description && charity.description.toLowerCase().includes(searchTerm.toLowerCase()));
    const matchesCategory = selectedCategory === "all" || charity.category_name === selectedCategory;
    return matchesSearch && matchesCategory;
  });

  const isCharityFollowed = (charityId: number) => {
    return userFollows.some(follow => follow.charity_id === charityId);
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-background p-4 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
          <p>Loading charities...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-background p-4">
      <div className="container mx-auto max-w-6xl">
        {/* Header */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold mb-2">Discover Causes</h1>
          <p className="text-muted-foreground">Find verified nonprofits and churches to support with your donations</p>
        </div>

        {/* Search and Filters */}
        <div className="mb-8 space-y-4">
          <Input
            placeholder="Search for causes, organizations, or keywords..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="max-w-md"
          />
          
          <div className="flex flex-wrap gap-2">
            {categories.map((category) => (
              <Button
                key={category.id}
                variant={selectedCategory === category.name ? "default" : "outline"}
                size="sm"
                onClick={() => setSelectedCategory(category.name)}
              >
                {category.description || category.name}
                {category.icon && <span className="ml-1">{category.icon}</span>}
              </Button>
            ))}
          </div>
        </div>

        {/* Charity Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {filteredCharities.map((charity) => (
            <Card key={charity.id} className="hover:shadow-lg transition-shadow">
              <CardHeader>
                <div className="flex items-start justify-between">
                  <div className="flex-1">
                    <CardTitle className="text-lg flex items-center gap-2">
                      {charity.name}
                      {charity.verified && (
                        <Check className="w-4 h-4 text-green-600" />
                      )}
                    </CardTitle>
                    <CardDescription className="mt-2">
                      {charity.description}
                    </CardDescription>
                  </div>
                </div>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex items-center gap-2 text-sm text-muted-foreground">
                  <Users className="w-4 h-4" />
                  <span>{charity.total_donors.toLocaleString()} donors</span>
                  <span>•</span>
                  <span>${(charity.total_raised / 1000000).toFixed(1)}M raised</span>
                </div>

                <div className="space-y-2">
                  <Badge variant="secondary" className="text-xs">
                    {charity.category_name}
                    {charity.category_icon && <span className="ml-1">{charity.category_icon}</span>}
                  </Badge>
                  {charity.impact_statement && (
                    <div className="text-sm font-medium text-green-600">
                      {charity.impact_statement}
                    </div>
                  )}
                  <div className="flex items-center gap-1 text-sm text-muted-foreground">
                    <span>⭐ {charity.rating.toFixed(1)}</span>
                  </div>
                </div>

                <div className="flex gap-2">
                  <Button
                    variant={isCharityFollowed(charity.id) ? "default" : "outline"}
                    size="sm"
                    className="flex-1"
                    onClick={() => handleFollowToggle(charity.id, isCharityFollowed(charity.id))}
                  >
                    {isCharityFollowed(charity.id) ? (
                      <>
                        <Heart className="w-4 h-4 mr-1 fill-current" />
                        Following
                      </>
                    ) : (
                      <>
                        <Heart className="w-4 h-4 mr-1" />
                        Follow
                      </>
                    )}
                  </Button>
                  <Button variant="outline" size="sm">
                    <DollarSign className="w-4 h-4" />
                  </Button>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>

        {filteredCharities.length === 0 && (
          <div className="text-center py-12">
            <p className="text-muted-foreground">No charities found matching your criteria.</p>
            <Button variant="outline" className="mt-4" onClick={() => {
              setSearchTerm("");
              setSelectedCategory("all");
            }}>
              Clear Filters
            </Button>
          </div>
        )}
      </div>
    </div>
  );
};

export default Charities;
