
import { useState } from 'react';
import { PlaidLink } from '@/components/PlaidLink';
import { LinkedAccounts } from '@/components/LinkedAccounts';

const Accounts = () => {
  const [refreshKey, setRefreshKey] = useState(0);

  const handleLinkSuccess = () => {
    // Trigger a refresh of linked accounts
    setRefreshKey(prev => prev + 1);
  };

  return (
    <div className="min-h-screen bg-background p-4">
      <div className="container mx-auto max-w-4xl">
        {/* Header */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold mb-2">Account Management</h1>
          <p className="text-muted-foreground">
            Link your bank accounts to automatically round up purchases and donate to charity
          </p>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          {/* Link Account Section */}
          <PlaidLink onSuccess={handleLinkSuccess} />

          {/* Linked Accounts Section */}
          <div key={refreshKey}>
            <LinkedAccounts />
          </div>
        </div>
      </div>
    </div>
  );
};

export default Accounts;
